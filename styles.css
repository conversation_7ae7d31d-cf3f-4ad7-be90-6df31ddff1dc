* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', <PERSON><PERSON>, 'Helvetica Neue', Aria<PERSON>, sans-serif;
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    color: #2c3e50;
    line-height: 1.6;
    font-size: 14px;
}

.container {
    display: flex;
    height: 100vh;
}

/* Sidebar Styles */
.sidebar {
    width: 260px;
    background: linear-gradient(180deg, #ffffff 0%, #f8f9fa 100%);
    border-right: 1px solid #e1e8ed;
    padding: 24px 0;
    box-shadow: 2px 0 10px rgba(0, 0, 0, 0.05);
}

.logo {
    display: flex;
    align-items: center;
    padding: 0 24px;
    margin-bottom: 32px;
    font-weight: 600;
    font-size: 18px;
    color: #00d4aa;
}

.logo-img {
    width: 28px;
    height: 28px;
    margin-right: 10px;
    border-radius: 6px;
}

.nav-menu {
    list-style: none;
}

.nav-item {
    display: flex;
    align-items: center;
    padding: 14px 24px;
    cursor: pointer;
    transition: all 0.3s ease;
    margin: 2px 12px;
    border-radius: 8px;
    font-weight: 500;
    color: #5a6c7d;
}

.nav-item:hover {
    background-color: #f0f8f7;
    color: #00d4aa;
    transform: translateX(4px);
}

.nav-icon {
    margin-right: 14px;
    font-size: 18px;
    width: 20px;
    text-align: center;
}

/* Main Content Styles */
.main-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    background-color: #fafbfc;
}

.header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 18px 32px;
    background: linear-gradient(90deg, #ffffff 0%, #f8f9fa 100%);
    border-bottom: 1px solid #e1e8ed;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
}

.language-selector {
    display: flex;
    align-items: center;
    cursor: pointer;
    padding: 8px 12px;
    border-radius: 6px;
    transition: all 0.2s ease;
    font-weight: 500;
    color: #5a6c7d;
}

.language-selector:hover {
    background-color: #f0f8f7;
    color: #00d4aa;
}

.dropdown-arrow {
    margin-left: 6px;
    font-size: 12px;
    transition: transform 0.2s ease;
}

.language-selector:hover .dropdown-arrow {
    transform: rotate(180deg);
}

.user-actions {
    display: flex;
    gap: 16px;
}

.cart-icon, .user-icon {
    font-size: 22px;
    cursor: pointer;
    padding: 8px;
    border-radius: 50%;
    transition: all 0.2s ease;
    color: #5a6c7d;
}

.cart-icon:hover, .user-icon:hover {
    background-color: #f0f8f7;
    color: #00d4aa;
    transform: scale(1.1);
}

/* Profile Container Styles */
.profile-container {
    max-width: 720px;
    margin: 40px auto;
    background: linear-gradient(145deg, #ffffff 0%, #f8fffe 100%);
    border-radius: 24px;
    padding: 48px 60px;
    text-align: center;
    box-shadow: 0 20px 40px rgba(0, 212, 170, 0.1), 0 8px 16px rgba(0, 0, 0, 0.05);
    border: 1px solid rgba(0, 212, 170, 0.1);
    min-height: 600px;
}

.profile-header h2 {
    margin-bottom: 36px;
    font-size: 28px;
    color: #2c3e50;
    font-weight: 600;
    letter-spacing: -0.5px;
}

.profile-avatar {
    margin-bottom: 36px;
}

.avatar-circle {
    position: relative;
    width: 88px;
    height: 88px;
    background: linear-gradient(145deg, #ffffff 0%, #f0f8f7 100%);
    border-radius: 50%;
    margin: 0 auto;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 42px;
    box-shadow: 0 8px 16px rgba(0, 212, 170, 0.15), inset 0 2px 4px rgba(255, 255, 255, 0.8);
    border: 2px solid rgba(0, 212, 170, 0.2);
    transition: all 0.3s ease;
}

.avatar-circle:hover {
    transform: scale(1.05);
    box-shadow: 0 12px 24px rgba(0, 212, 170, 0.2);
}

.avatar-status {
    position: absolute;
    bottom: 6px;
    right: 6px;
    width: 22px;
    height: 22px;
    background: linear-gradient(145deg, #00d4aa 0%, #00c299 100%);
    border-radius: 50%;
    border: 3px solid #fff;
    box-shadow: 0 2px 4px rgba(0, 212, 170, 0.3);
}

/* Form Styles */
.profile-form {
    text-align: left;
}

.form-group {
    margin-bottom: 28px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: #2c3e50;
    font-size: 14px;
    letter-spacing: 0.2px;
}

.form-group label::after {
    content: "";
    display: inline-block;
    width: 4px;
    height: 4px;
    background-color: #00d4aa;
    border-radius: 50%;
    margin-left: 6px;
    vertical-align: middle;
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 16px 20px;
    border: 2px solid #e1e8ed;
    border-radius: 12px;
    font-size: 15px;
    background: linear-gradient(145deg, #ffffff 0%, #fafbfc 100%);
    transition: all 0.3s ease;
    font-family: inherit;
    min-height: 48px;
    box-sizing: border-box;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: #00d4aa;
    box-shadow: 0 0 0 4px rgba(0, 212, 170, 0.1), 0 4px 12px rgba(0, 212, 170, 0.15);
    background-color: #ffffff;
    transform: translateY(-1px);
}

.form-group input::placeholder,
.form-group textarea::placeholder {
    color: #a0aec0;
    font-style: italic;
}

.save-btn {
    width: 100%;
    padding: 16px 24px;
    background: linear-gradient(145deg, #00d4aa 0%, #00c299 100%);
    color: white;
    border: none;
    border-radius: 12px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 12px rgba(0, 212, 170, 0.3), 0 2px 4px rgba(0, 0, 0, 0.1);
    letter-spacing: 0.5px;
    text-transform: uppercase;
    margin-top: 8px;
}

.save-btn:hover {
    background: linear-gradient(145deg, #00c299 0%, #00b088 100%);
    transform: translateY(-2px);
    box-shadow: 0 6px 16px rgba(0, 212, 170, 0.4), 0 4px 8px rgba(0, 0, 0, 0.15);
}

.save-btn:active {
    transform: translateY(0);
    box-shadow: 0 2px 8px rgba(0, 212, 170, 0.3);
}

/* Modal Styles */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.modal-content {
    background: linear-gradient(145deg, #ffffff 0%, #f8fffe 100%);
    border-radius: 24px;
    max-width: 900px;
    max-height: 90vh;
    width: 95%;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    box-shadow: 0 24px 48px rgba(0, 0, 0, 0.15), 0 12px 24px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(0, 212, 170, 0.1);
}

.modal-header {
    padding: 40px 48px 0 48px;
    flex-shrink: 0;
    background: linear-gradient(145deg, #ffffff 0%, #f8fffe 100%);
    border-radius: 24px 24px 0 0;
}

.modal-header h2 {
    text-align: center;
    margin-bottom: 12px;
    font-size: 28px;
    color: #2c3e50;
    font-weight: 700;
    letter-spacing: -0.5px;
}

.modal-subtitle {
    text-align: center;
    margin-bottom: 32px;
    color: #5a6c7d;
    font-size: 15px;
    line-height: 1.5;
    font-weight: 500;
}

.modal-body {
    flex: 1;
    overflow-y: auto;
    padding: 0 48px;
    background-color: #fafbfc;
}

.company-form {
    display: flex;
    flex-direction: column;
    gap: 24px;
    padding: 16px 0;
}

.form-row {
    display: flex;
    gap: 24px;
}

.half-width {
    flex: 1;
}

.input-with-currency {
    position: relative;
}

.currency {
    position: absolute;
    right: 16px;
    top: 50%;
    transform: translateY(-50%);
    color: #00d4aa;
    font-weight: 600;
    font-size: 16px;
    background-color: #f0f8f7;
    padding: 4px 8px;
    border-radius: 6px;
    border: 1px solid rgba(0, 212, 170, 0.2);
}

/* File Input Styles */
.form-group input[type="file"] {
    padding: 12px;
    border: 2px dashed #e1e8ed;
    background: linear-gradient(145deg, #fafbfc 0%, #f8f9fa 100%);
    cursor: pointer;
    transition: all 0.3s ease;
}

.form-group input[type="file"]:hover {
    border-color: #00d4aa;
    background: linear-gradient(145deg, #f0f8f7 0%, #e8f5f3 100%);
}

.form-group input[type="file"]:focus {
    border-color: #00d4aa;
    border-style: solid;
}

/* Required Field Indicator */
.form-group label[for="firstName"]::before,
.form-group label[for="lastName"]::before,
.form-group label[for="position"]::before,
.form-group label[for="country"]::before,
.form-group label[for="companyName"]::before,
.form-group label[for="businessType"]::before,
.form-group label[for="email"]::before,
.form-group label[for="registrationNumber"]::before {
    content: "*";
    color: #e74c3c;
    font-weight: bold;
    margin-right: 4px;
    font-size: 16px;
}

/* Select Dropdown Styling */
.form-group select {
    appearance: none;
    background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%2300d4aa' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6,9 12,15 18,9'%3e%3c/polyline%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: right 16px center;
    background-size: 16px;
    padding-right: 48px;
}

.modal-footer {
    flex-shrink: 0;
    padding: 24px 48px 40px 48px;
    border-top: 1px solid rgba(0, 212, 170, 0.1);
    background: linear-gradient(145deg, #ffffff 0%, #f8fffe 100%);
    border-radius: 0 0 24px 24px;
}

.form-actions {
    display: flex;
    gap: 16px;
    margin: 0;
}

.skip-btn {
    flex: 1;
    padding: 16px 24px;
    background: linear-gradient(145deg, #f8f9fa 0%, #e9ecef 100%);
    color: #5a6c7d;
    border: 2px solid #e1e8ed;
    border-radius: 12px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    letter-spacing: 0.3px;
    text-transform: uppercase;
}

.skip-btn:hover {
    background: linear-gradient(145deg, #e9ecef 0%, #dee2e6 100%);
    border-color: #adb5bd;
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.submit-btn {
    flex: 1;
    padding: 16px 24px;
    background: linear-gradient(145deg, #00d4aa 0%, #00c299 100%);
    color: white;
    border: none;
    border-radius: 12px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 12px rgba(0, 212, 170, 0.3), 0 2px 4px rgba(0, 0, 0, 0.1);
    letter-spacing: 0.5px;
    text-transform: uppercase;
}

.submit-btn:hover {
    background: linear-gradient(145deg, #00c299 0%, #00b088 100%);
    transform: translateY(-2px);
    box-shadow: 0 6px 16px rgba(0, 212, 170, 0.4), 0 4px 8px rgba(0, 0, 0, 0.15);
}

.submit-btn:active {
    transform: translateY(0);
    box-shadow: 0 2px 8px rgba(0, 212, 170, 0.3);
}

/* Animation Keyframes */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInLeft {
    from {
        opacity: 0;
        transform: translateX(-30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes pulse {
    0%, 100% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
}

/* Apply animations */
.profile-container {
    animation: fadeInUp 0.6s ease-out;
}

.modal-content {
    animation: fadeInUp 0.5s ease-out;
}

.nav-item {
    animation: slideInLeft 0.4s ease-out;
}

.nav-item:nth-child(1) { animation-delay: 0.1s; }
.nav-item:nth-child(2) { animation-delay: 0.2s; }
.nav-item:nth-child(3) { animation-delay: 0.3s; }
.nav-item:nth-child(4) { animation-delay: 0.4s; }
.nav-item:nth-child(5) { animation-delay: 0.5s; }
.nav-item:nth-child(6) { animation-delay: 0.6s; }
.nav-item:nth-child(7) { animation-delay: 0.7s; }

.avatar-status {
    animation: pulse 2s infinite;
}

/* Responsive Design */
@media (max-width: 768px) {
    .container {
        flex-direction: column;
    }

    .sidebar {
        width: 100%;
        height: auto;
        padding: 16px 0;
    }

    .nav-item {
        padding: 10px 16px;
        margin: 1px 8px;
    }

    .modal-content {
        width: 98%;
        max-height: 95vh;
        margin: 2.5vh auto;
        max-width: none;
    }

    .modal-header {
        padding: 32px 32px 0 32px;
    }

    .modal-body {
        padding: 0 32px;
    }

    .modal-footer {
        padding: 24px 32px 32px 32px;
    }

    .form-row {
        flex-direction: column;
        gap: 16px;
    }

    .form-actions {
        flex-direction: column;
        gap: 12px;
    }

    .profile-container {
        margin: 20px;
        padding: 40px 32px;
        max-width: none;
    }

    .header {
        padding: 12px 16px;
    }
}

@media (max-width: 480px) {
    .modal-content {
        width: 98%;
        border-radius: 16px;
    }

    .profile-container {
        border-radius: 16px;
        margin: 10px;
    }

    .form-group input,
    .form-group select,
    .form-group textarea {
        padding: 12px 14px;
        font-size: 16px; /* Prevents zoom on iOS */
    }
}
