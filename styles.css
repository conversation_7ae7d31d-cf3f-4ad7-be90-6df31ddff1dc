* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background-color: #f5f5f5;
    color: #333;
}

.container {
    display: flex;
    height: 100vh;
}

/* Sidebar Styles */
.sidebar {
    width: 250px;
    background-color: #fff;
    border-right: 1px solid #e0e0e0;
    padding: 20px 0;
}

.logo {
    display: flex;
    align-items: center;
    padding: 0 20px;
    margin-bottom: 30px;
}

.logo-img {
    width: 24px;
    height: 24px;
    margin-right: 8px;
}

.nav-menu {
    list-style: none;
}

.nav-item {
    display: flex;
    align-items: center;
    padding: 12px 20px;
    cursor: pointer;
    transition: background-color 0.2s;
}

.nav-item:hover {
    background-color: #f8f9fa;
}

.nav-icon {
    margin-right: 12px;
    font-size: 16px;
}

/* Main Content Styles */
.main-content {
    flex: 1;
    display: flex;
    flex-direction: column;
}

.header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 30px;
    background-color: #fff;
    border-bottom: 1px solid #e0e0e0;
}

.language-selector {
    display: flex;
    align-items: center;
    cursor: pointer;
}

.dropdown-arrow {
    margin-left: 5px;
    font-size: 12px;
}

.user-actions {
    display: flex;
    gap: 15px;
}

.cart-icon, .user-icon {
    font-size: 20px;
    cursor: pointer;
}

/* Profile Container Styles */
.profile-container {
    max-width: 500px;
    margin: 50px auto;
    background-color: #e8f5f3;
    border-radius: 20px;
    padding: 40px;
    text-align: center;
}

.profile-header h2 {
    margin-bottom: 30px;
    font-size: 24px;
    color: #333;
}

.profile-avatar {
    margin-bottom: 30px;
}

.avatar-circle {
    position: relative;
    width: 80px;
    height: 80px;
    background-color: #fff;
    border-radius: 50%;
    margin: 0 auto;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 40px;
}

.avatar-status {
    position: absolute;
    bottom: 5px;
    right: 5px;
    width: 20px;
    height: 20px;
    background-color: #00d4aa;
    border-radius: 50%;
    border: 3px solid #fff;
}

/* Form Styles */
.profile-form {
    text-align: left;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: 500;
    color: #555;
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 12px;
    border: 1px solid #ddd;
    border-radius: 8px;
    font-size: 14px;
    background-color: #fff;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: #00d4aa;
    box-shadow: 0 0 0 2px rgba(0, 212, 170, 0.1);
}

.save-btn {
    width: 100%;
    padding: 15px;
    background-color: #00d4aa;
    color: white;
    border: none;
    border-radius: 8px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: background-color 0.2s;
}

.save-btn:hover {
    background-color: #00c299;
}

/* Modal Styles */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.modal-content {
    background-color: #e8f5f3;
    border-radius: 20px;
    padding: 30px;
    max-width: 600px;
    max-height: 90vh;
    overflow-y: auto;
    width: 90%;
}

.modal-header h2 {
    text-align: center;
    margin-bottom: 10px;
    font-size: 24px;
    color: #333;
}

.modal-subtitle {
    text-align: center;
    margin-bottom: 30px;
    color: #666;
    font-size: 14px;
}

.company-form {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.form-row {
    display: flex;
    gap: 15px;
}

.half-width {
    flex: 1;
}

.input-with-currency {
    position: relative;
}

.currency {
    position: absolute;
    right: 12px;
    top: 50%;
    transform: translateY(-50%);
    color: #666;
    font-weight: 500;
}

.form-actions {
    display: flex;
    gap: 15px;
    margin-top: 20px;
}

.skip-btn {
    flex: 1;
    padding: 15px;
    background-color: #f8f9fa;
    color: #666;
    border: 1px solid #ddd;
    border-radius: 8px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s;
}

.skip-btn:hover {
    background-color: #e9ecef;
    border-color: #adb5bd;
}

.submit-btn {
    flex: 1;
    padding: 15px;
    background-color: #00d4aa;
    color: white;
    border: none;
    border-radius: 8px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: background-color 0.2s;
}

.submit-btn:hover {
    background-color: #00c299;
}

/* Responsive Design */
@media (max-width: 768px) {
    .container {
        flex-direction: column;
    }
    
    .sidebar {
        width: 100%;
        height: auto;
    }
    
    .modal-content {
        width: 95%;
        padding: 20px;
    }
    
    .form-row {
        flex-direction: column;
    }
    
    .form-actions {
        flex-direction: column;
    }
}
